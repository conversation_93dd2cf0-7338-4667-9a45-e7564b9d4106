.hero {
  position: relative;
  min-height: 80vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #e3f2fd 0%, #fff8e1 50%, #fce4ec 100%);
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
}

.hero-content {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  z-index: 2;
}

.hero-text {
  animation: slideInLeft 1s ease-out;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 1rem;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  font-size: 1.5rem;
  color: #ff9800;
  margin-bottom: 1rem;
  font-weight: 600;
}

.hero-description {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.hero-button {
  background: linear-gradient(45deg, #ff9800, #ffb74d);
  border: none;
  padding: 1rem 2rem;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.hero-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 152, 0, 0.4);
  background: linear-gradient(45deg, #f57c00, #ff9800);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
  animation: slideInRight 1s ease-out;
}

.pet-illustration {
  position: relative;
  width: 300px;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pet-dog, .pet-cat {
  font-size: 4rem;
  position: absolute;
  animation: float 3s ease-in-out infinite;
}

.pet-dog {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.pet-cat {
  bottom: 20%;
  right: 20%;
  animation-delay: 1.5s;
}

.pet-heart {
  font-size: 2rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s ease-in-out infinite;
}

/* 装饰性元素 */
.hero-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration {
  position: absolute;
  font-size: 2rem;
  opacity: 0.3;
  animation: float 4s ease-in-out infinite;
}

.decoration-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.decoration-2 {
  top: 20%;
  right: 15%;
  animation-delay: 1s;
}

.decoration-3 {
  bottom: 30%;
  left: 5%;
  animation-delay: 2s;
}

.decoration-4 {
  bottom: 10%;
  right: 10%;
  animation-delay: 3s;
}

.decoration-5 {
  top: 50%;
  left: 2%;
  animation-delay: 1.5s;
}

/* 动画效果 */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  .pet-illustration {
    width: 200px;
    height: 200px;
  }
  
  .pet-dog, .pet-cat {
    font-size: 3rem;
  }
  
  .decoration {
    font-size: 1.5rem;
  }
}
