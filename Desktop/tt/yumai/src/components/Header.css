.header {
  background: linear-gradient(135deg, #e3f2fd 0%, #fff3e0 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 3px solid #ff9800;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

/* Logo区域 */
.logo-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.logo-icon {
  font-size: 2rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.logo-text {
  font-size: 1.8rem;
  font-weight: bold;
  color: #1976d2;
  margin: 0;
  font-family: 'Arial', sans-serif;
}

.logo-subtitle {
  font-size: 0.9rem;
  color: #ff9800;
  font-weight: 500;
}

/* 导航菜单 */
.nav-menu {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-item {
  background: none;
  border: none;
  font-size: 1.1rem;
  font-weight: 500;
  color: #424242;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-item:hover {
  background: rgba(255, 152, 0, 0.1);
  color: #ff9800;
  transform: translateY(-2px);
}

.nav-item::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 0;
  height: 2px;
  background: #ff9800;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-item:hover::after {
  width: 80%;
}

/* 购物车 */
.cart-section {
  position: relative;
}

.cart-button {
  background: #ff9800;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.cart-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
}

.cart-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #f44336;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* 移动端菜单按钮 */
.mobile-menu-button {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #424242;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-container {
    padding: 1rem;
  }
  
  .logo-text {
    font-size: 1.5rem;
  }
  
  .logo-subtitle {
    display: none;
  }
  
  .nav-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    flex-direction: column;
    padding: 1rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    gap: 0.5rem;
  }
  
  .nav-menu-open {
    display: flex;
  }
  
  .mobile-menu-button {
    display: block;
  }
  
  .nav-item {
    width: 100%;
    text-align: center;
    padding: 1rem;
  }
}
