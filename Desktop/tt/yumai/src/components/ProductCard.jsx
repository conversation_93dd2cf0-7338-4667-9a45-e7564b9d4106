import { useState } from 'react';
import './ProductCard.css';

const ProductCard = ({ product, onAddToCart }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isAdding, setIsAdding] = useState(false);

  const handleAddToCart = async () => {
    setIsAdding(true);
    
    // 模拟添加到购物车的过程
    setTimeout(() => {
      onAddToCart(product);
      setIsAdding(false);
    }, 500);
  };

  const discountPercentage = product.originalPrice 
    ? Math.round((1 - product.price / product.originalPrice) * 100)
    : 0;

  return (
    <div 
      className={`product-card ${isHovered ? 'product-card-hovered' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 商品标签 */}
      <div className="product-badges">
        {product.isHot && <span className="badge badge-hot">🔥 热销</span>}
        {product.isNew && <span className="badge badge-new">✨ 新品</span>}
        {discountPercentage > 0 && (
          <span className="badge badge-discount">-{discountPercentage}%</span>
        )}
      </div>

      {/* 商品图片 */}
      <div className="product-image">
        <div className="product-emoji">{product.image}</div>
        <div className="product-overlay">
          <button 
            className="quick-view-btn"
            onClick={() => alert(`查看 ${product.name} 详情`)}
          >
            👁️ 快速预览
          </button>
        </div>
      </div>

      {/* 商品信息 */}
      <div className="product-info">
        <h3 className="product-name">{product.name}</h3>
        <p className="product-description">{product.description}</p>
        
        {/* 商品特性 */}
        <div className="product-features">
          {product.features.map((feature, index) => (
            <span key={index} className="feature-tag">
              {feature}
            </span>
          ))}
        </div>

        {/* 价格信息 */}
        <div className="product-pricing">
          <span className="current-price">¥{product.price.toFixed(2)}</span>
          {product.originalPrice && (
            <span className="original-price">¥{product.originalPrice.toFixed(2)}</span>
          )}
        </div>

        {/* 库存信息 */}
        <div className="product-stock">
          <span className={`stock-indicator ${product.stock < 20 ? 'stock-low' : ''}`}>
            📦 库存: {product.stock}
          </span>
        </div>

        {/* 添加到购物车按钮 */}
        <button 
          className={`add-to-cart-btn ${isAdding ? 'adding' : ''}`}
          onClick={handleAddToCart}
          disabled={isAdding || product.stock === 0}
        >
          {isAdding ? (
            <>
              <span className="loading-spinner"></span>
              添加中...
            </>
          ) : product.stock === 0 ? (
            '暂时缺货'
          ) : (
            <>
              🛒 加入购物车
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ProductCard;
