import './Hero.css';

const Hero = () => {
  const scrollToProducts = () => {
    const element = document.getElementById('products');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="hero" className="hero">
      <div className="hero-background">
        <div className="hero-overlay"></div>
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              欢迎来到玉米宠物用品商城！
            </h1>
            <p className="hero-subtitle">
              为您的爱宠挑选最优质的食品与玩具
            </p>
            <p className="hero-description">
              我们致力于为每一位毛孩子提供健康、安全、高品质的产品，
              让您的宠物享受最好的生活品质。
            </p>
            <button 
              className="hero-button"
              onClick={scrollToProducts}
            >
              查看商品 🐕
            </button>
          </div>
          <div className="hero-image">
            <div className="pet-illustration">
              <div className="pet-dog">🐕</div>
              <div className="pet-cat">🐱</div>
              <div className="pet-heart">💖</div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 装饰性元素 */}
      <div className="hero-decorations">
        <div className="decoration decoration-1">🦴</div>
        <div className="decoration decoration-2">🎾</div>
        <div className="decoration decoration-3">🐾</div>
        <div className="decoration decoration-4">🏠</div>
        <div className="decoration decoration-5">🥎</div>
      </div>
    </section>
  );
};

export default Hero;
