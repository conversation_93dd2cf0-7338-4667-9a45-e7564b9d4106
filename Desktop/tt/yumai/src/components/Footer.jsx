import './Footer.css';

const Footer = () => {
  return (
    <footer id="contact" className="footer">
      <div className="footer-container">
        <div className="footer-content">
          {/* 关于我们 */}
          <div className="footer-section">
            <h3 className="footer-title">关于玉米</h3>
            <p className="footer-description">
              我们致力于为所有宠物提供健康、安全、高品质的产品。
              每一件商品都经过精心挑选，只为给您的爱宠最好的呵护。
            </p>
            <div className="footer-mission">
              <span className="mission-icon">💝</span>
              <span>用心呵护每一个毛孩子</span>
            </div>
          </div>

          {/* 联系方式 */}
          <div className="footer-section">
            <h3 className="footer-title">联系我们</h3>
            <div className="contact-info">
              <div className="contact-item">
                <span className="contact-icon">📞</span>
                <span>客服热线：400-888-6666</span>
              </div>
              <div className="contact-item">
                <span className="contact-icon">💬</span>
                <span>微信客服：yumai_pet</span>
              </div>
              <div className="contact-item">
                <span className="contact-icon">📧</span>
                <span>邮箱：<EMAIL></span>
              </div>
              <div className="contact-item">
                <span className="contact-icon">🕒</span>
                <span>营业时间：9:00-21:00</span>
              </div>
            </div>
          </div>

          {/* 社交媒体 */}
          <div className="footer-section">
            <h3 className="footer-title">关注我们</h3>
            <div className="social-links">
              <button className="social-link weibo">
                <span className="social-icon">🌟</span>
                <span>微博</span>
              </button>
              <button className="social-link xiaohongshu">
                <span className="social-icon">📖</span>
                <span>小红书</span>
              </button>
              <button className="social-link wechat">
                <span className="social-icon">💬</span>
                <span>微信公众号</span>
              </button>
            </div>
            <p className="social-description">
              关注我们获取最新优惠信息和宠物护理小贴士！
            </p>
          </div>
        </div>

        {/* 底部版权 */}
        <div className="footer-bottom">
          <div className="footer-divider"></div>
          <div className="footer-copyright">
            <p>© 2024 玉米宠物用品商城 | 用爱守护每一个毛孩子</p>
            <div className="footer-pets">
              <span>🐕</span>
              <span>🐱</span>
              <span>🐰</span>
              <span>🐹</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
