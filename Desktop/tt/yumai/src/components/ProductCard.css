.product-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.product-card-hovered .product-overlay {
  opacity: 1;
}

/* 商品标签 */
.product-badges {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.badge-hot {
  background: linear-gradient(45deg, #ff5722, #ff9800);
}

.badge-new {
  background: linear-gradient(45deg, #4caf50, #8bc34a);
}

.badge-discount {
  background: linear-gradient(45deg, #e91e63, #f06292);
}

/* 商品图片 */
.product-image {
  position: relative;
  height: 200px;
  background: linear-gradient(135deg, #e3f2fd, #fff8e1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.product-emoji {
  font-size: 4rem;
  transition: all 0.3s ease;
}

.product-card:hover .product-emoji {
  transform: scale(1.1);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.quick-view-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.quick-view-btn:hover {
  background: white;
  transform: scale(1.05);
}

/* 商品信息 */
.product-info {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #1976d2;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.product-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0 0 1rem 0;
  flex: 1;
}

/* 商品特性 */
.product-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.feature-tag {
  background: rgba(25, 118, 210, 0.1);
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* 价格信息 */
.product-pricing {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.8rem;
}

.current-price {
  font-size: 1.4rem;
  font-weight: bold;
  color: #ff9800;
}

.original-price {
  font-size: 1rem;
  color: #999;
  text-decoration: line-through;
}

/* 库存信息 */
.product-stock {
  margin-bottom: 1rem;
}

.stock-indicator {
  font-size: 0.9rem;
  color: #4caf50;
  font-weight: 500;
}

.stock-low {
  color: #ff5722;
}

/* 添加到购物车按钮 */
.add-to-cart-btn {
  background: linear-gradient(45deg, #ff9800, #ffb74d);
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1rem;
  margin-top: auto;
}

.add-to-cart-btn:hover:not(:disabled) {
  background: linear-gradient(45deg, #f57c00, #ff9800);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.add-to-cart-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.add-to-cart-btn.adding {
  background: #4caf50;
}

/* 加载动画 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-image {
    height: 150px;
  }
  
  .product-emoji {
    font-size: 3rem;
  }
  
  .product-info {
    padding: 1rem;
  }
  
  .product-name {
    font-size: 1.1rem;
  }
  
  .current-price {
    font-size: 1.2rem;
  }
}
