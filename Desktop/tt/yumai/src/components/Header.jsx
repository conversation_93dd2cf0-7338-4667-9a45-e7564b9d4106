import { useState } from 'react';
import './Header.css';

const Header = ({ cartCount = 0 }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  return (
    <header className="header">
      <div className="header-container">
        {/* Logo区域 */}
        <div className="logo-section">
          <div className="logo-icon">🐾</div>
          <h1 className="logo-text">玉米</h1>
          <span className="logo-subtitle">宠物用品商城</span>
        </div>

        {/* 导航菜单 */}
        <nav className={`nav-menu ${isMenuOpen ? 'nav-menu-open' : ''}`}>
          <button 
            className="nav-item"
            onClick={() => scrollToSection('hero')}
          >
            首页
          </button>
          <button 
            className="nav-item"
            onClick={() => scrollToSection('products')}
          >
            商品分类
          </button>
          <button 
            className="nav-item"
            onClick={() => scrollToSection('contact')}
          >
            联系我们
          </button>
        </nav>

        {/* 购物车 */}
        <div className="cart-section">
          <button className="cart-button">
            🛒
            {cartCount > 0 && <span className="cart-count">{cartCount}</span>}
          </button>
        </div>

        {/* 移动端菜单按钮 */}
        <button 
          className="mobile-menu-button"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          ☰
        </button>
      </div>
    </header>
  );
};

export default Header;
