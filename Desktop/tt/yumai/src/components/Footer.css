.footer {
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 50%, #ff9800 100%);
  color: white;
  padding: 3rem 0 1rem;
  margin-top: 4rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  margin-bottom: 2rem;
}

.footer-section {
  animation: fadeInUp 0.8s ease-out;
}

.footer-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #fff;
  position: relative;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 50px;
  height: 3px;
  background: #ffeb3b;
  border-radius: 2px;
}

.footer-description {
  line-height: 1.6;
  margin-bottom: 1rem;
  color: rgba(255, 255, 255, 0.9);
}

.footer-mission {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.8rem;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.mission-icon {
  font-size: 1.2rem;
}

/* 联系信息 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.contact-icon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

/* 社交媒体 */
.social-links {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 0.8rem 1rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.social-link.weibo:hover {
  background: rgba(255, 87, 34, 0.3);
}

.social-link.xiaohongshu:hover {
  background: rgba(233, 30, 99, 0.3);
}

.social-link.wechat:hover {
  background: rgba(76, 175, 80, 0.3);
}

.social-icon {
  font-size: 1.2rem;
}

.social-description {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* 底部版权 */
.footer-bottom {
  margin-top: 2rem;
}

.footer-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  margin-bottom: 1rem;
}

.footer-copyright {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.footer-pets {
  display: flex;
  gap: 0.5rem;
  font-size: 1.2rem;
}

.footer-pets span {
  animation: bounce 2s infinite;
  animation-delay: calc(var(--i) * 0.2s);
}

.footer-pets span:nth-child(1) { --i: 0; }
.footer-pets span:nth-child(2) { --i: 1; }
.footer-pets span:nth-child(3) { --i: 2; }
.footer-pets span:nth-child(4) { --i: 3; }

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .footer-copyright {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .social-links {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .social-link {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }
}
