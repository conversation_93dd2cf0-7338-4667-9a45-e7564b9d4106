export const productCategories = [
  {
    id: 'all',
    name: '全部商品',
    icon: '🏪'
  },
  {
    id: 'dog',
    name: '狗狗用品',
    icon: '🐕'
  },
  {
    id: 'cat',
    name: '猫咪用品',
    icon: '🐱'
  },
  {
    id: 'care',
    name: '宠物护理',
    icon: '🧴'
  }
];

export const products = [
  // 狗狗用品
  {
    id: 1,
    name: '天然牛肉味狗粮',
    category: 'dog',
    price: 89.00,
    originalPrice: 109.00,
    image: '🥩',
    description: '适合中大型犬，富含蛋白质和维生素，促进健康成长',
    features: ['天然无添加', '营养均衡', '易消化'],
    isHot: true,
    stock: 50
  },
  {
    id: 2,
    name: '互动益智狗玩具',
    category: 'dog',
    price: 45.00,
    originalPrice: 55.00,
    image: '🎾',
    description: '训练狗狗智力，缓解无聊，增进主人与宠物的感情',
    features: ['益智训练', '安全材质', '耐咬耐玩'],
    isNew: true,
    stock: 30
  },
  {
    id: 3,
    name: '舒适狗窝',
    category: 'dog',
    price: 128.00,
    originalPrice: 158.00,
    image: '🏠',
    description: '柔软舒适，四季适用，给狗狗一个温暖的家',
    features: ['可拆洗', '防滑底部', '保暖透气'],
    stock: 20
  },
  {
    id: 4,
    name: '狗狗牵引绳套装',
    category: 'dog',
    price: 35.00,
    originalPrice: 45.00,
    image: '🦮',
    description: '安全舒适的牵引绳，让散步更安心',
    features: ['反光设计', '舒适手柄', '可调节长度'],
    stock: 40
  },

  // 猫咪用品
  {
    id: 5,
    name: '营养猫粮',
    category: 'cat',
    price: 75.00,
    originalPrice: 95.00,
    image: '🐟',
    description: '精选深海鱼肉，营养丰富，呵护猫咪健康',
    features: ['深海鱼肉', '毛球护理', '营养全面'],
    isHot: true,
    stock: 60
  },
  {
    id: 6,
    name: '逗猫棒玩具',
    category: 'cat',
    price: 25.00,
    originalPrice: 35.00,
    image: '🪶',
    description: '激发猫咪狩猎天性，增加运动量，促进身心健康',
    features: ['天然羽毛', '可伸缩', '互动性强'],
    stock: 80
  },
  {
    id: 7,
    name: '自动猫砂盆',
    category: 'cat',
    price: 299.00,
    originalPrice: 399.00,
    image: '🚽',
    description: '智能清洁，省时省力，保持环境清洁',
    features: ['自动清洁', '除臭功能', '静音设计'],
    isNew: true,
    stock: 15
  },
  {
    id: 8,
    name: '猫爬架',
    category: 'cat',
    price: 188.00,
    originalPrice: 228.00,
    image: '🏗️',
    description: '多层设计，满足猫咪攀爬和休息需求',
    features: ['多层设计', '稳固结构', '剑麻材质'],
    stock: 25
  },

  // 宠物护理
  {
    id: 9,
    name: '宠物洗毛液',
    category: 'care',
    price: 38.00,
    originalPrice: 48.00,
    image: '🧴',
    description: '温和配方，深层清洁，让毛发柔顺亮泽',
    features: ['温和无刺激', '深层清洁', '护毛配方'],
    stock: 70
  },
  {
    id: 10,
    name: '除蚤喷雾',
    category: 'care',
    price: 42.00,
    originalPrice: 52.00,
    image: '💨',
    description: '天然植物配方，有效驱除跳蚤，保护宠物健康',
    features: ['天然植物', '安全有效', '长效保护'],
    isHot: true,
    stock: 45
  },
  {
    id: 11,
    name: '宠物梳子套装',
    category: 'care',
    price: 28.00,
    originalPrice: 38.00,
    image: '🪮',
    description: '专业梳理工具，去除死毛，保持毛发健康',
    features: ['多种规格', '防静电', '舒适握柄'],
    stock: 55
  },
  {
    id: 12,
    name: '宠物指甲剪',
    category: 'care',
    price: 22.00,
    originalPrice: 32.00,
    image: '✂️',
    description: '安全锋利，轻松修剪，保护家具和主人',
    features: ['安全设计', '锋利耐用', '防滑手柄'],
    stock: 35
  }
];

// 获取热门商品
export const getHotProducts = () => {
  return products.filter(product => product.isHot);
};

// 获取新品推荐
export const getNewProducts = () => {
  return products.filter(product => product.isNew);
};

// 根据分类获取商品
export const getProductsByCategory = (categoryId) => {
  if (categoryId === 'all') {
    return products;
  }
  return products.filter(product => product.category === categoryId);
};
